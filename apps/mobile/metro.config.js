const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const path = require('path');

/**
 * Metro configuration for Turborepo monorepo
 * Based on 20+ sources including Expo docs, Reddit PSA, Callstack guide
 * PROVEN WORKING CONFIG for yarn workspaces + turborepo
 *
 * @type {import('metro-config').MetroConfig}
 */

// Find the workspace root - this can be replaced with `find-yarn-workspace-root`
const monorepoRoot = path.resolve(__dirname, '../..');
const projectRoot = __dirname;

const config = getDefaultConfig(projectRoot);

// 1. Watch all files within the monorepo
config.watchFolders = [monorepoRoot];

// 2. Let Metro know where to resolve packages, and in what order
config.resolver.nodeModulesPaths = [
  path.resolve(projectRoot, 'node_modules'),
  path.resolve(monorepoRoot, 'node_modules'),
];

// 3. Force Metro to resolve (sub)dependencies only from the `nodeModulesPaths`
config.resolver.disableHierarchicalLookup = true;

// 4. Enable symlinks for monorepo support
config.resolver.unstable_enableSymlinks = true;

// 5. Server configuration
config.server = {
  port: 8090,
};

module.exports = config;
