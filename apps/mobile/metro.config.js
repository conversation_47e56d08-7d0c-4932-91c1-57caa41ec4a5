const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const path = require('path');

/**
 * Metro configuration for Turborepo monorepo
 * Based on React Native 0.77+ official symlink support
 * Sources: React Native docs, Medium articles, GitHub issues
 *
 * @type {import('metro-config').MetroConfig}
 */

// Monorepo root directory
const monorepoRoot = path.resolve(__dirname, '../..');

const config = {
  projectRoot: __dirname,

  // Watch folders - critical for monorepo
  watchFolders: [
    path.resolve(monorepoRoot, 'packages'),
    path.resolve(monorepoRoot, 'node_modules'),
    path.resolve(__dirname, 'node_modules'),
  ],

  resolver: {
    // CRITICAL: Enable package exports support (React Native 0.72+)
    unstable_enablePackageExports: true,

    // Node modules resolution paths - order matters!
    nodeModulesPaths: [
      path.resolve(__dirname, 'node_modules'),
      path.resolve(monorepoRoot, 'node_modules'),
    ],

    // Resolver main fields
    resolverMainFields: ['react-native', 'browser', 'main'],

    // Enable symlink resolution
    unstable_enableSymlinks: true,
  },

  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
