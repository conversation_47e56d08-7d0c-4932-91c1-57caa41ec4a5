const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const path = require('path');

/**
 * Metro configuration for Turborepo monorepo
 * FIXED: Proper dependency resolution for yarn workspaces
 * Forces Metro to use root node_modules for all dependencies
 *
 * @type {import('metro-config').MetroConfig}
 */

// Monorepo root directory
const monorepoRoot = path.resolve(__dirname, '../..');

const config = {
  // CRITICAL: Set projectRoot to monorepo root, not mobile app
  projectRoot: monorepoRoot,

  // Watch folders - include all necessary directories
  watchFolders: [
    path.resolve(monorepoRoot, 'apps'),
    path.resolve(monorepoRoot, 'packages'),
    path.resolve(monorepoRoot, 'node_modules'),
  ],

  resolver: {
    // CRITICAL: Enable package exports support
    unstable_enablePackageExports: true,

    // ONLY use root node_modules - this is the key fix
    nodeModulesPaths: [
      path.resolve(monorepoRoot, 'node_modules'),
    ],

    // Resolver main fields
    resolverMainFields: ['react-native', 'browser', 'main'],

    // Enable symlink resolution
    unstable_enableSymlinks: true,

    // CRITICAL: Disable hierarchical lookup to force root resolution
    disableHierarchicalLookup: true,
  },

  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
  },

  // Server configuration
  server: {
    port: 8090,
  },
};

// CRITICAL: Use monorepo root as base for default config
module.exports = mergeConfig(getDefaultConfig(monorepoRoot), config);
