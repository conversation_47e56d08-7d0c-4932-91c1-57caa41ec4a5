const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const path = require('path');

/**
 * Metro configuration for Turborepo monorepo
 * Optimized for single Metro instance to prevent conflicts
 * Based on React Native 0.77+ official symlink support
 *
 * @type {import('metro-config').MetroConfig}
 */

// Monorepo root directory
const monorepoRoot = path.resolve(__dirname, '../..');

const config = {
  projectRoot: __dirname,

  // Watch folders - critical for monorepo
  watchFolders: [
    path.resolve(monorepoRoot, 'packages'),
    path.resolve(monorepoRoot, 'node_modules'),
  ],

  resolver: {
    // CRITICAL: Enable package exports support (React Native 0.72+)
    unstable_enablePackageExports: true,

    // Node modules resolution paths - mobile first, then root
    nodeModulesPaths: [
      path.resolve(__dirname, 'node_modules'),
      path.resolve(monorepoRoot, 'node_modules'),
    ],

    // Resolver main fields
    resolverMainFields: ['react-native', 'browser', 'main'],

    // Enable symlink resolution for monorepo
    unstable_enableSymlinks: true,

    // Disable hierarchical lookup to prevent conflicts
    disableHierarchicalLookup: false,
  },

  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
  },

  // Server configuration to prevent port conflicts
  server: {
    port: 8090,
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
